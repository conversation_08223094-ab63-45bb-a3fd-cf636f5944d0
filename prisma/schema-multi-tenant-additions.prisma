// Multi-Tenant Schema Additions
// This file contains new tables to be added to the existing schema
// These are additive changes only - no modifications to existing tables

// User-Clinic Role Mapping for Multi-Tenant Access
model UserClinicRole {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  clinicId  String   @map("clinic_id")
  role      String   // clinic_admin, provider, staff, viewer
  isActive  Boolean  @default(true) @map("is_active")
  createdBy String?  @map("created_by")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Note: Foreign keys will be added in Phase 3
  // user   User   @relation(fields: [userId], references: [id])
  // clinic Clinic @relation(fields: [clinicId], references: [id])
  
  @@unique([userId, clinicId])
  @@index([clinicId])
  @@index([userId])
  @@map("user_clinic_roles")
}

// Goal Templates for Reusable Goal Configurations
model GoalTemplate {
  id                 String   @id @default(cuid())
  name               String
  description        String?
  category           String   // financial, patient, operational
  metricDefinitionId String   @map("metric_definition_id")
  targetFormula      String?  @map("target_formula") // e.g., "previous_month * 1.1"
  timePeriod         String   @map("time_period") // daily, weekly, monthly, quarterly, annual
  isSystemTemplate   Boolean  @default(false) @map("is_system_template")
  clinicId           String?  @map("clinic_id") // NULL for system templates
  createdBy          String?  @map("created_by")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId])
  @@index([metricDefinitionId])
  @@map("goal_templates")
}

// Advanced Financial Metrics Tracking
model FinancialMetric {
  id                String   @id @default(cuid())
  clinicId          String   @map("clinic_id")
  date              DateTime
  metricType        String   @map("metric_type") // production, collection, adjustment, refund
  category          String   // procedure_type, insurance_type, payment_method
  amount            Decimal  @db.Decimal(10, 2)
  providerId        String?  @map("provider_id")
  insuranceCarrier  String?  @map("insurance_carrier")
  paymentMethod     String?  @map("payment_method")
  procedureCode     String?  @map("procedure_code")
  notes             String?
  sourceReference   String?  @map("source_reference") // External system reference
  createdBy         String?  @map("created_by")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId, date])
  @@index([providerId])
  @@index([metricType, date])
  @@map("financial_metrics")
}

// Appointment Analytics
model AppointmentMetric {
  id                  String   @id @default(cuid())
  clinicId            String   @map("clinic_id")
  date                DateTime
  providerId          String?  @map("provider_id")
  appointmentType     String   @map("appointment_type") // new_patient, recall, emergency, treatment
  scheduledCount      Int      @map("scheduled_count")
  completedCount      Int      @map("completed_count")
  cancelledCount      Int      @map("cancelled_count")
  noShowCount         Int      @map("no_show_count")
  averageDuration     Int?     @map("average_duration") // in minutes
  productionAmount    Decimal? @map("production_amount") @db.Decimal(10, 2)
  utilizationRate     Decimal? @map("utilization_rate") @db.Decimal(5, 2) // percentage
  createdBy           String?  @map("created_by")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId, date])
  @@index([providerId])
  @@map("appointment_metrics")
}

// Call Tracking Performance
model CallMetric {
  id                    String   @id @default(cuid())
  clinicId              String   @map("clinic_id")
  date                  DateTime
  callType              String   @map("call_type") // hygiene_recall, treatment_followup, new_patient
  totalCalls            Int      @map("total_calls")
  connectedCalls        Int      @map("connected_calls")
  voicemails            Int
  appointmentsScheduled Int      @map("appointments_scheduled")
  conversionRate        Decimal  @map("conversion_rate") @db.Decimal(5, 2) // percentage
  averageCallDuration   Int?     @map("average_call_duration") // in seconds
  staffMemberId         String?  @map("staff_member_id")
  createdBy             String?  @map("created_by")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId, date])
  @@index([staffMemberId])
  @@map("call_metrics")
}

// Patient Analytics
model PatientMetric {
  id                   String   @id @default(cuid())
  clinicId             String   @map("clinic_id")
  date                 DateTime
  activePatients       Int      @map("active_patients")
  newPatients          Int      @map("new_patients")
  reactivatedPatients  Int      @map("reactivated_patients")
  lostPatients         Int      @map("lost_patients")
  patientRetentionRate Decimal  @map("patient_retention_rate") @db.Decimal(5, 2) // percentage
  averagePatientValue  Decimal? @map("average_patient_value") @db.Decimal(10, 2)
  recareComplianceRate Decimal? @map("recare_compliance_rate") @db.Decimal(5, 2) // percentage
  treatmentAcceptanceRate Decimal? @map("treatment_acceptance_rate") @db.Decimal(5, 2) // percentage
  createdBy            String?  @map("created_by")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId, date])
  @@map("patient_metrics")
}

// Pre-computed Metric Aggregations for Performance
model MetricAggregation {
  id                 String   @id @default(cuid())
  clinicId           String   @map("clinic_id")
  metricDefinitionId String   @map("metric_definition_id")
  aggregationType    String   @map("aggregation_type") // daily, weekly, monthly, quarterly, yearly
  periodStart        DateTime @map("period_start")
  periodEnd          DateTime @map("period_end")
  value              Decimal  @db.Decimal(20, 4)
  count              Int      // Number of data points in aggregation
  minimum            Decimal? @db.Decimal(20, 4)
  maximum            Decimal? @db.Decimal(20, 4)
  average            Decimal? @db.Decimal(20, 4)
  standardDeviation  Decimal? @map("standard_deviation") @db.Decimal(20, 4)
  providerId         String?  @map("provider_id")
  metadata           Json?    // Additional aggregation metadata
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  @@unique([clinicId, metricDefinitionId, aggregationType, periodStart, providerId])
  @@index([clinicId, periodStart])
  @@index([metricDefinitionId])
  @@map("metric_aggregations")
}

// Google Credentials Management (from target schema)
model GoogleCredential {
  id           String   @id @default(cuid())
  clinicId     String   @map("clinic_id")
  userId       String   @map("user_id")
  accessToken  String   @map("access_token") // Encrypted
  refreshToken String   @map("refresh_token") // Encrypted
  expiresAt    DateTime @map("expires_at")
  scope        String[]
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  
  @@unique([clinicId, userId])
  @@index([clinicId])
  @@index([userId])
  @@map("google_credentials")
}

// Spreadsheet Connections (from target schema)
model SpreadsheetConnection {
  id             String   @id @default(cuid())
  clinicId       String   @map("clinic_id")
  credentialId   String   @map("credential_id")
  spreadsheetId  String   @map("spreadsheet_id")
  spreadsheetName String  @map("spreadsheet_name")
  sheetNames     String[] @map("sheet_names")
  lastSyncAt     DateTime? @map("last_sync_at")
  syncStatus     String   @default("active") @map("sync_status") // active, paused, error
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  
  @@unique([clinicId, spreadsheetId])
  @@index([clinicId])
  @@index([credentialId])
  @@map("spreadsheet_connections")
}

// Enhanced Column Mappings (from target schema)
model ColumnMappingV2 {
  id            String   @id @default(cuid())
  connectionId  String   @map("connection_id")
  sheetName     String   @map("sheet_name")
  mappingConfig Json     @map("mapping_config") // JSONB for flexible configuration
  templateName  String?  @map("template_name")
  version       Int      @default(1)
  isActive      Boolean  @default(true) @map("is_active")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  @@index([connectionId])
  @@map("column_mappings_v2")
}