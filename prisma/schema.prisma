// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Clinics
model Clinic {
  id               String   @id @default(cuid())
  name             String
  location         String
  status           String   // active/inactive
  registrationCode String?  @unique @map("registration_code") // For users to join clinic
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  
  // Phase 2: UUID Migration Field
  uuidId           String?  @unique @map("uuid_id") @db.Uuid
  
  users     User[]
  providers Provider[]
  metrics   MetricValue[]
  goals     Goal[]
  dataSources DataSource[]
  hygieneProduction HygieneProduction[]
  dentistProduction DentistProduction[]
  locations Location[] // New: clinic can have multiple locations
  locationFinancials LocationFinancial[] // New: location financial data
  metricAggregations MetricAggregation[]

  @@map("clinics")
}

// Users
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  role      String   // office_manager, dentist, front_desk, admin
  lastLogin DateTime? @map("last_login")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Phase 2: Auth Integration Fields
  authId    String?  @unique @map("auth_id") // Supabase auth.users.id
  uuidId    String?  @unique @map("uuid_id") @db.Uuid // Future primary key
  
  clinicId  String?  @map("clinic_id") // Optional for system admins
  clinic    Clinic?  @relation(fields: [clinicId], references: [id])
  dashboards Dashboard[]

  @@map("users")
}

// Providers - Global provider identity
model Provider {
  id           String   @id @default(cuid())
  name         String   // Full name (generated from firstName + lastName)
  firstName    String?  @map("first_name") // Captured during Google Apps Script setup
  lastName     String?  @map("last_name")  // Captured during Google Apps Script setup
  email        String?  @unique // Unique identifier across all locations
  providerType String   @map("provider_type") // dentist, hygienist, specialist
  position     String?  // More detailed position info from setup
  status       String   // active/inactive
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  
  // Primary clinic relationship - most providers work primarily at one location
  clinicId     String   @map("clinic_id")
  clinic       Clinic   @relation(fields: [clinicId], references: [id])
  
  metrics      MetricValue[]
  goals        Goal[]
  dataSources  DataSource[] // Link providers to their data sources
  hygieneProduction HygieneProduction[]
  dentistProduction DentistProduction[]
  providerLocations ProviderLocation[] // New: provider-location relationships
  metricAggregations MetricAggregation[]

  @@map("providers")
}

// Locations - Specific clinic locations (e.g., Humble, Baytown)
model Location {
  id          String   @id @default(cuid())
  clinicId    String   @map("clinic_id")
  name        String   // "Baytown", "Humble"
  address     String?
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  clinic      Clinic   @relation(fields: [clinicId], references: [id])
  financials  LocationFinancial[]
  providers   ProviderLocation[]
  
  @@index([clinicId])
  @@unique([clinicId, name]) // Prevent duplicate location names per clinic
  @@map("locations")
}

// Provider-Location Relationships - Many-to-many with additional metadata
model ProviderLocation {
  id         String   @id @default(cuid())
  providerId String   @map("provider_id")
  locationId String   @map("location_id")
  isActive   Boolean  @default(true) @map("is_active")
  startDate  DateTime @map("start_date")
  endDate    DateTime? @map("end_date")
  isPrimary  Boolean  @default(false) @map("is_primary") // Primary location for reporting
  
  provider   Provider @relation(fields: [providerId], references: [id])
  location   Location @relation(fields: [locationId], references: [id])
  
  @@unique([providerId, locationId])
  @@index([providerId])
  @@index([locationId])
  @@map("provider_locations")
}

// Location Financial Data - Structured financial metrics per location
model LocationFinancial {
  id              String   @id @default(cuid())
  clinicId        String   @map("clinic_id")
  locationId      String   @map("location_id")  // FK to Location instead of string
  date            DateTime
  production      Decimal  @db.Decimal(10, 2)
  adjustments     Decimal  @db.Decimal(10, 2)
  writeOffs       Decimal  @db.Decimal(10, 2)
  netProduction   Decimal  @db.Decimal(10, 2)  // Calculated field
  patientIncome   Decimal  @db.Decimal(10, 2)
  insuranceIncome Decimal  @db.Decimal(10, 2)
  totalCollections Decimal @db.Decimal(10, 2)  // Calculated field
  unearned        Decimal? @db.Decimal(10, 2)
  dataSourceId    String?  @map("data_source_id")
  createdBy       String?  @map("created_by")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  clinic          Clinic      @relation(fields: [clinicId], references: [id])
  location        Location    @relation(fields: [locationId], references: [id])
  dataSource      DataSource? @relation(fields: [dataSourceId], references: [id])
  
  @@index([clinicId, date])
  @@index([locationId, date])
  @@index([clinicId, locationId, date])
  @@unique([clinicId, locationId, date]) // Prevent duplicate entries
  @@map("location_financial")
}

// Metric Definitions
model MetricDefinition {
  id                String   @id @default(cuid())
  name              String
  description       String
  dataType          String   @map("data_type") // currency, percentage, integer, date
  calculationFormula String?  @map("calculation_formula")
  category          String   // financial, patient, appointment, provider, treatment
  isComposite       Boolean  @map("is_composite")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  
  metrics           MetricValue[]
  columnMappings    ColumnMapping[]
  goals             Goal[]
  widgets           Widget[]
  metricAggregations MetricAggregation[]

  @@map("metric_definitions")
}

// Data Sources (Google Sheets)
model DataSource {
  id              String   @id @default(cuid())
  name            String
  spreadsheetId   String   @map("spreadsheet_id")
  sheetName       String   @map("sheet_name")
  lastSyncedAt    DateTime? @map("last_synced_at")
  syncFrequency   String   @map("sync_frequency")
  connectionStatus String   @map("connection_status")
  appScriptId     String?  @map("app_script_id")

  accessToken     String   @map("access_token")
  refreshToken    String?  @map("refresh_token")
  expiryDate      DateTime? @map("expiry_date")
  
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  columnMappings  ColumnMapping[]
  metrics         MetricValue[]
  hygieneProduction HygieneProduction[]
  dentistProduction DentistProduction[]
  locationFinancials LocationFinancial[] // New: location financial data

  clinicId        String   @map("clinic_id")
  clinic          Clinic   @relation(fields: [clinicId], references: [id])
  
  providerId      String?  @map("provider_id") // Link to specific provider
  provider        Provider? @relation(fields: [providerId], references: [id])

  @@map("data_sources")
}

// Column Mappings
model ColumnMapping {
  id                 String   @id @default(cuid())
  columnName         String   @map("column_name")
  transformationRule String?  @map("transformation_rule")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  dataSourceId       String   @map("data_source_id")
  dataSource         DataSource @relation(fields: [dataSourceId], references: [id])
  
  metricDefinitionId String   @map("metric_definition_id")
  metricDefinition   MetricDefinition @relation(fields: [metricDefinitionId], references: [id])

  @@map("column_mappings")
}

// Metric Values
model MetricValue {
  id                 String   @id @default(cuid())
  date               DateTime
  value              String   // Using string to handle various data types
  sourceType         String   @map("source_type") // spreadsheet, form
  sourceSheet        String?  @map("source_sheet")
  externalId         String?  @map("external_id")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  metricDefinitionId String   @map("metric_definition_id")
  metricDefinition   MetricDefinition @relation(fields: [metricDefinitionId], references: [id])
  
  clinicId           String?  @map("clinic_id")
  clinic             Clinic?  @relation(fields: [clinicId], references: [id])
  
  providerId         String?  @map("provider_id")
  provider           Provider? @relation(fields: [providerId], references: [id])
  
  dataSourceId       String?  @map("data_source_id")
  dataSource         DataSource? @relation(fields: [dataSourceId], references: [id])

  @@map("metric_values")
}

// Goals
model Goal {
  id                 String   @id @default(cuid())
  timePeriod         String   @map("time_period") // daily, weekly, monthly, quarterly, annual
  startDate          DateTime @map("start_date")
  endDate            DateTime @map("end_date")
  targetValue        String   @map("target_value")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  metricDefinitionId String   @map("metric_definition_id")
  metricDefinition   MetricDefinition @relation(fields: [metricDefinitionId], references: [id])
  
  clinicId           String?  @map("clinic_id")
  clinic             Clinic?  @relation(fields: [clinicId], references: [id])
  
  providerId         String?  @map("provider_id")
  provider           Provider? @relation(fields: [providerId], references: [id])

  @@map("goals")
}

// Dashboards
model Dashboard {
  id           String   @id @default(cuid())
  name         String
  isDefault    Boolean  @default(false) @map("is_default")
  layoutConfig Json?    @map("layout_config")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  
  // Phase 2: UUID Migration Fields
  uuidId       String?  @unique @map("uuid_id") @db.Uuid
  userUuidId   String?  @map("user_uuid_id")
  
  userId       String   @map("user_id")
  user         User     @relation(fields: [userId], references: [id])
  widgets      Widget[]

  @@map("dashboards")
}

// Widgets
model Widget {
  id                 String   @id @default(cuid())
  widgetType         String   @map("widget_type") // chart, counter, table
  chartType          String?  @map("chart_type") // line, bar, pie, etc.
  positionX          Int      @map("position_x")
  positionY          Int      @map("position_y")
  width              Int
  height             Int
  config             Json?
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  dashboardId        String   @map("dashboard_id")
  dashboard          Dashboard @relation(fields: [dashboardId], references: [id])
  
  metricDefinitionId String?  @map("metric_definition_id")
  metricDefinition   MetricDefinition? @relation(fields: [metricDefinitionId], references: [id])

  @@map("widgets")
}

// ===== MULTI-TENANT SCHEMA ADDITIONS =====
// Phase 1: Additive changes only - no modifications to existing tables

// User-Clinic Role Mapping for Multi-Tenant Access
model UserClinicRole {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  clinicId  String   @map("clinic_id")
  role      String   // clinic_admin, provider, staff, viewer
  isActive  Boolean  @default(true) @map("is_active")
  createdBy String?  @map("created_by")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Note: Foreign keys will be added in Phase 3
  // user   User   @relation(fields: [userId], references: [id])
  // clinic Clinic @relation(fields: [clinicId], references: [id])
  
  @@unique([userId, clinicId])
  @@index([clinicId])
  @@index([userId])
  @@map("user_clinic_roles")
}

// Goal Templates for Reusable Goal Configurations
model GoalTemplate {
  id                 String   @id @default(cuid())
  name               String
  description        String?
  category           String   // financial, patient, operational
  metricDefinitionId String   @map("metric_definition_id")
  targetFormula      String?  @map("target_formula") // e.g., "previous_month * 1.1"
  timePeriod         String   @map("time_period") // daily, weekly, monthly, quarterly, annual
  isSystemTemplate   Boolean  @default(false) @map("is_system_template")
  clinicId           String?  @map("clinic_id") // NULL for system templates
  createdBy          String?  @map("created_by")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId])
  @@index([metricDefinitionId])
  @@map("goal_templates")
}

// Advanced Financial Metrics Tracking
model FinancialMetric {
  id                String   @id @default(cuid())
  clinicId          String   @map("clinic_id")
  date              DateTime
  metricType        String   @map("metric_type") // production, collection, adjustment, refund
  category          String   // procedure_type, insurance_type, payment_method
  amount            Decimal  @db.Decimal(10, 2)
  providerId        String?  @map("provider_id")
  insuranceCarrier  String?  @map("insurance_carrier")
  paymentMethod     String?  @map("payment_method")
  procedureCode     String?  @map("procedure_code")
  notes             String?
  sourceReference   String?  @map("source_reference") // External system reference
  createdBy         String?  @map("created_by")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId, date])
  @@index([providerId])
  @@index([metricType, date])
  @@map("financial_metrics")
}

// Appointment Analytics
model AppointmentMetric {
  id                  String   @id @default(cuid())
  clinicId            String   @map("clinic_id")
  date                DateTime
  providerId          String?  @map("provider_id")
  appointmentType     String   @map("appointment_type") // new_patient, recall, emergency, treatment
  scheduledCount      Int      @map("scheduled_count")
  completedCount      Int      @map("completed_count")
  cancelledCount      Int      @map("cancelled_count")
  noShowCount         Int      @map("no_show_count")
  averageDuration     Int?     @map("average_duration") // in minutes
  productionAmount    Decimal? @map("production_amount") @db.Decimal(10, 2)
  utilizationRate     Decimal? @map("utilization_rate") @db.Decimal(5, 2) // percentage
  createdBy           String?  @map("created_by")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId, date])
  @@index([providerId])
  @@map("appointment_metrics")
}

// Call Tracking Performance
model CallMetric {
  id                    String   @id @default(cuid())
  clinicId              String   @map("clinic_id")
  date                  DateTime
  callType              String   @map("call_type") // hygiene_recall, treatment_followup, new_patient
  totalCalls            Int      @map("total_calls")
  connectedCalls        Int      @map("connected_calls")
  voicemails            Int
  appointmentsScheduled Int      @map("appointments_scheduled")
  conversionRate        Decimal  @map("conversion_rate") @db.Decimal(5, 2) // percentage
  averageCallDuration   Int?     @map("average_call_duration") // in seconds
  staffMemberId         String?  @map("staff_member_id")
  createdBy             String?  @map("created_by")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId, date])
  @@index([staffMemberId])
  @@map("call_metrics")
}

// Patient Analytics
model PatientMetric {
  id                   String   @id @default(cuid())
  clinicId             String   @map("clinic_id")
  date                 DateTime
  activePatients       Int      @map("active_patients")
  newPatients          Int      @map("new_patients")
  reactivatedPatients  Int      @map("reactivated_patients")
  lostPatients         Int      @map("lost_patients")
  patientRetentionRate Decimal  @map("patient_retention_rate") @db.Decimal(5, 2) // percentage
  averagePatientValue  Decimal? @map("average_patient_value") @db.Decimal(10, 2)
  recareComplianceRate Decimal? @map("recare_compliance_rate") @db.Decimal(5, 2) // percentage
  treatmentAcceptanceRate Decimal? @map("treatment_acceptance_rate") @db.Decimal(5, 2) // percentage
  createdBy            String?  @map("created_by")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")
  
  @@index([clinicId, date])
  @@map("patient_metrics")
}

// Pre-computed Metric Aggregations for Performance
model MetricAggregation {
  id                 String   @id @default(cuid())
  clinicId           String   @map("clinic_id")
  metricDefinitionId String   @map("metric_definition_id")
  aggregationType    String   @map("aggregation_type") // daily, weekly, monthly, quarterly, yearly
  periodStart        DateTime @map("period_start")
  periodEnd          DateTime @map("period_end")
  value              Decimal  @db.Decimal(20, 4)
  count              Int      // Number of data points in aggregation
  minimum            Decimal? @db.Decimal(20, 4)
  maximum            Decimal? @db.Decimal(20, 4)
  average            Decimal? @db.Decimal(20, 4)
  standardDeviation  Decimal? @map("standard_deviation") @db.Decimal(20, 4)
  providerId         String?  @map("provider_id")
  metadata           Json?    // Additional aggregation metadata
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  // Relations
  clinic            Clinic            @relation(fields: [clinicId], references: [id])
  metricDefinition  MetricDefinition  @relation(fields: [metricDefinitionId], references: [id])
  provider          Provider?         @relation(fields: [providerId], references: [id])
  
  @@unique([clinicId, metricDefinitionId, aggregationType, periodStart, providerId])
  @@index([clinicId, periodStart])
  @@index([metricDefinitionId])
  @@index([providerId])
  @@map("metric_aggregations")
}

// Google Credentials Management (from target schema)
model GoogleCredential {
  id           String   @id @default(cuid())
  clinicId     String   @map("clinic_id")
  userId       String   @map("user_id")
  accessToken  String   @map("access_token") // Encrypted
  refreshToken String   @map("refresh_token") // Encrypted
  expiresAt    DateTime @map("expires_at")
  scope        String[]
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  
  @@unique([clinicId, userId])
  @@index([clinicId])
  @@index([userId])
  @@map("google_credentials")
}

// Spreadsheet Connections (from target schema)
model SpreadsheetConnection {
  id             String   @id @default(cuid())
  clinicId       String   @map("clinic_id")
  credentialId   String   @map("credential_id")
  spreadsheetId  String   @map("spreadsheet_id")
  spreadsheetName String  @map("spreadsheet_name")
  sheetNames     String[] @map("sheet_names")
  lastSyncAt     DateTime? @map("last_sync_at")
  syncStatus     String   @default("active") @map("sync_status") // active, paused, error
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  
  @@unique([clinicId, spreadsheetId])
  @@index([clinicId])
  @@index([credentialId])
  @@map("spreadsheet_connections")
}

// Enhanced Column Mappings (from target schema)
model ColumnMappingV2 {
  id            String   @id @default(cuid())
  connectionId  String   @map("connection_id")
  sheetName     String   @map("sheet_name")
  mappingConfig Json     @map("mapping_config") // JSONB for flexible configuration
  templateName  String?  @map("template_name")
  version       Int      @default(1)
  isActive      Boolean  @default(true) @map("is_active")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  @@index([connectionId])
  @@map("column_mappings_v2")
}

// ===== HYGIENE PRODUCTION TRACKING =====

// Dedicated table for hygiene production data with enhanced provider tracking
model HygieneProduction {
  id                  String   @id @default(cuid())
  date                DateTime
  monthTab            String   @map("month_tab") // e.g., "Dec-23"
  hoursWorked         Decimal? @map("hours_worked") @db.Decimal(5, 2)
  estimatedProduction Decimal? @map("estimated_production") @db.Decimal(10, 2)
  verifiedProduction  Decimal? @map("verified_production") @db.Decimal(10, 2)
  productionGoal      Decimal? @map("production_goal") @db.Decimal(10, 2)
  variancePercentage  Decimal? @map("variance_percentage") @db.Decimal(5, 4)
  bonusAmount         Decimal? @map("bonus_amount") @db.Decimal(10, 2)
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")
  
  // Links to provider and clinic
  clinicId            String   @map("clinic_id")
  clinic              Clinic   @relation(fields: [clinicId], references: [id])
  
  providerId          String?  @map("provider_id")
  provider            Provider? @relation(fields: [providerId], references: [id])
  
  // For tracking sync source
  dataSourceId        String?  @map("data_source_id")
  dataSource          DataSource? @relation(fields: [dataSourceId], references: [id])
  
  @@index([clinicId, date])
  @@index([providerId, date])
  @@index([dataSourceId])
  @@map("hygiene_production")
}

// ===== DENTIST PRODUCTION TRACKING =====

// Dedicated table for dentist production data with dual-location tracking
model DentistProduction {
  id                      String   @id @default(cuid())
  date                    DateTime
  monthTab                String   @map("month_tab") // e.g., "Nov-24"
  verifiedProductionHumble   Decimal? @map("verified_production_humble") @db.Decimal(10, 2)
  verifiedProductionBaytown  Decimal? @map("verified_production_baytown") @db.Decimal(10, 2)
  totalProduction         Decimal? @map("total_production") @db.Decimal(10, 2)
  monthlyGoal             Decimal? @map("monthly_goal") @db.Decimal(10, 2)
  productionPerHour       Decimal? @map("production_per_hour") @db.Decimal(8, 2)
  avgDailyProduction      Decimal? @map("avg_daily_production") @db.Decimal(10, 2)
  providerName            String?  @map("provider_name") // e.g., "Dr. Obi" - kept for legacy data
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @updatedAt @map("updated_at")
  
  // Links to clinic
  clinicId                String   @map("clinic_id")
  clinic                  Clinic   @relation(fields: [clinicId], references: [id])
  
  // Links to provider (proper foreign key relationship)
  providerId              String?  @map("provider_id")
  provider                Provider? @relation(fields: [providerId], references: [id])
  
  // For tracking sync source
  dataSourceId            String?  @map("data_source_id")
  dataSource              DataSource? @relation(fields: [dataSourceId], references: [id])
  
  @@index([clinicId, date])
  @@index([providerId, date])
  @@index([providerName, date]) // Keep for legacy data
  @@index([dataSourceId])
  @@map("dentist_production")
}

// ===== PHASE 2: UUID MIGRATION SUPPORT =====

// ID Mapping Table for Migration Tracking
model IdMapping {
  id         String   @id @default(cuid())
  tableName  String   @map("table_name")
  oldId      String   @map("old_id")
  newId      String   @map("new_id")
  createdAt  DateTime @default(now()) @map("created_at")
  
  @@unique([tableName, oldId])
  @@index([tableName])
  @@map("id_mappings")
}