// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Clinics
model Clinic {
  id        String   @id @default(cuid())
  name      String
  location  String
  status    String   // active/inactive
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  users     User[]
  providers Provider[]
  metrics   MetricValue[]
  goals     Goal[]
  dataSources DataSource[]

  @@map("clinics")
}

// Users
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  role      String   // office_manager, dentist, front_desk, admin
  lastLogin DateTime? @map("last_login")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  clinicId  String   @map("clinic_id")
  clinic    Clinic   @relation(fields: [clinicId], references: [id])
  dashboards Dashboard[]

  @@map("users")
}

// Providers
model Provider {
  id           String   @id @default(cuid())
  name         String
  providerType String   @map("provider_type") // dentist, hygienist, specialist
  status       String   // active/inactive
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  
  clinicId     String   @map("clinic_id")
  clinic       Clinic   @relation(fields: [clinicId], references: [id])
  metrics      MetricValue[]
  goals        Goal[]

  @@map("providers")
}

// Metric Definitions
model MetricDefinition {
  id                String   @id @default(cuid())
  name              String
  description       String
  dataType          String   @map("data_type") // currency, percentage, integer, date
  calculationFormula String?  @map("calculation_formula")
  category          String   // financial, patient, appointment, provider, treatment
  isComposite       Boolean  @map("is_composite")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  
  metrics           MetricValue[]
  columnMappings    ColumnMapping[]
  goals             Goal[]
  widgets           Widget[]

  @@map("metric_definitions")
}

// Data Sources (Google Sheets)
model DataSource {
  id              String   @id @default(cuid())
  name            String
  spreadsheetId   String   @map("spreadsheet_id")
  sheetName       String   @map("sheet_name")
  lastSyncedAt    DateTime? @map("last_synced_at")
  syncFrequency   String   @map("sync_frequency")
  connectionStatus String   @map("connection_status")
  appScriptId     String?  @map("app_script_id")

  accessToken     String   @map("access_token")
  refreshToken    String?  @map("refresh_token")
  expiryDate      DateTime? @map("expiry_date")
  
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  columnMappings  ColumnMapping[]
  metrics         MetricValue[]

  clinicId        String   @map("clinic_id")
  clinic          Clinic   @relation(fields: [clinicId], references: [id])

  @@map("data_sources")
}

// Column Mappings
model ColumnMapping {
  id                 String   @id @default(cuid())
  columnName         String   @map("column_name")
  transformationRule String?  @map("transformation_rule")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  dataSourceId       String   @map("data_source_id")
  dataSource         DataSource @relation(fields: [dataSourceId], references: [id])
  
  metricDefinitionId String   @map("metric_definition_id")
  metricDefinition   MetricDefinition @relation(fields: [metricDefinitionId], references: [id])

  @@map("column_mappings")
}

// Metric Values
model MetricValue {
  id                 String   @id @default(cuid())
  date               DateTime
  value              String   // Using string to handle various data types
  sourceType         String   @map("source_type") // spreadsheet, form
  sourceSheet        String?  @map("source_sheet")
  externalId         String?  @map("external_id")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  metricDefinitionId String   @map("metric_definition_id")
  metricDefinition   MetricDefinition @relation(fields: [metricDefinitionId], references: [id])
  
  clinicId           String?  @map("clinic_id")
  clinic             Clinic?  @relation(fields: [clinicId], references: [id])
  
  providerId         String?  @map("provider_id")
  provider           Provider? @relation(fields: [providerId], references: [id])
  
  dataSourceId       String?  @map("data_source_id")
  dataSource         DataSource? @relation(fields: [dataSourceId], references: [id])

  @@map("metric_values")
}

// Goals
model Goal {
  id                 String   @id @default(cuid())
  timePeriod         String   @map("time_period") // daily, weekly, monthly, quarterly, annual
  startDate          DateTime @map("start_date")
  endDate            DateTime @map("end_date")
  targetValue        String   @map("target_value")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  metricDefinitionId String   @map("metric_definition_id")
  metricDefinition   MetricDefinition @relation(fields: [metricDefinitionId], references: [id])
  
  clinicId           String?  @map("clinic_id")
  clinic             Clinic?  @relation(fields: [clinicId], references: [id])
  
  providerId         String?  @map("provider_id")
  provider           Provider? @relation(fields: [providerId], references: [id])

  @@map("goals")
}

// Dashboards
model Dashboard {
  id           String   @id @default(cuid())
  name         String
  isDefault    Boolean  @default(false) @map("is_default")
  layoutConfig Json?    @map("layout_config")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  
  userId       String   @map("user_id")
  user         User     @relation(fields: [userId], references: [id])
  widgets      Widget[]

  @@map("dashboards")
}

// Widgets
model Widget {
  id                 String   @id @default(cuid())
  widgetType         String   @map("widget_type") // chart, counter, table
  chartType          String?  @map("chart_type") // line, bar, pie, etc.
  positionX          Int      @map("position_x")
  positionY          Int      @map("position_y")
  width              Int
  height             Int
  config             Json?
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  dashboardId        String   @map("dashboard_id")
  dashboard          Dashboard @relation(fields: [dashboardId], references: [id])
  
  metricDefinitionId String?  @map("metric_definition_id")
  metricDefinition   MetricDefinition? @relation(fields: [metricDefinitionId], references: [id])

  @@map("widgets")
}