<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients for depth and polish -->
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:0.2" />
    </linearGradient>
    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <!-- Subtle shadow filter -->
    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Clean background -->
  <rect width="400" height="400" fill="#f8fafc"/>
  
  <!-- Floating abstract elements -->
  <circle cx="80" cy="60" r="3" fill="#dbeafe" opacity="0.6"/>
  <circle cx="320" cy="80" r="4" fill="#bfdbfe" opacity="0.7"/>
  <circle cx="350" cy="45" r="2" fill="#93c5fd" opacity="0.8"/>
  
  <!-- Main analytics card -->
  <rect x="60" y="120" width="180" height="120" rx="12" fill="url(#cardGradient)" 
        filter="url(#softShadow)" stroke="#e5e7eb" stroke-width="1"/>
  
  <!-- Chart area with subtle grid -->
  <rect x="80" y="160" width="140" height="60" fill="#ffffff" rx="4"/>
  <line x1="90" y1="170" x2="210" y2="170" stroke="#f1f5f9" stroke-width="1"/>
  <line x1="90" y1="185" x2="210" y2="185" stroke="#f1f5f9" stroke-width="1"/>
  <line x1="90" y1="200" x2="210" y2="200" stroke="#f1f5f9" stroke-width="1"/>
  <line x1="90" y1="215" x2="210" y2="215" stroke="#f1f5f9" stroke-width="1"/>
  
  <!-- Beautiful trending line with area fill -->
  <path d="M90 210 Q120 200 140 180 T190 170 L190 220 L90 220 Z" 
        fill="url(#chartGradient)"/>
  <path d="M90 210 Q120 200 140 180 T190 170" 
        stroke="#2563eb" stroke-width="3" fill="none" stroke-linecap="round"/>
  
  <!-- Data points with subtle pulse animation -->
  <circle cx="90" cy="210" r="4" fill="#2563eb">
    <animate attributeName="r" values="4;5;4" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="140" cy="180" r="4" fill="#2563eb">
    <animate attributeName="r" values="4;5;4" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="190" cy="170" r="4" fill="#2563eb">
    <animate attributeName="r" values="4;5;4" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Chart title -->
  <text x="150" y="145" text-anchor="middle" fill="#1e293b" font-family="Inter, sans-serif" 
        font-size="14" font-weight="600">Revenue Growth</text>
  
  <!-- Success metrics card -->
  <rect x="280" y="140" width="80" height="80" rx="12" fill="url(#cardGradient)" 
        filter="url(#softShadow)" stroke="#e5e7eb" stroke-width="1"/>
  
  <!-- Success checkmark with circle -->
  <circle cx="320" cy="170" r="16" fill="url(#successGradient)"/>
  <path d="M312 170 L318 176 L328 166" stroke="white" stroke-width="2.5" 
        stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- Success metric -->
  <text x="320" y="200" text-anchor="middle" fill="#1e293b" font-family="Inter, sans-serif" 
        font-size="18" font-weight="700">98%</text>
  <text x="320" y="212" text-anchor="middle" fill="#6b7280" font-family="Inter, sans-serif" 
        font-size="10" font-weight="500">SATISFACTION</text>
  
  <!-- Modern bar chart -->
  <g transform="translate(80, 280)">
    <rect x="0" y="30" width="20" height="30" rx="10" fill="#bfdbfe"/>
    <rect x="30" y="20" width="20" height="40" rx="10" fill="#93c5fd"/>
    <rect x="60" y="10" width="20" height="50" rx="10" fill="#60a5fa"/>
    <rect x="90" y="0" width="20" height="60" rx="10" fill="#2563eb"/>
    
    <!-- Subtle labels -->
    <text x="10" y="75" text-anchor="middle" fill="#9ca3af" font-family="Inter, sans-serif" 
          font-size="9">Q1</text>
    <text x="40" y="75" text-anchor="middle" fill="#9ca3af" font-family="Inter, sans-serif" 
          font-size="9">Q2</text>
    <text x="70" y="75" text-anchor="middle" fill="#9ca3af" font-family="Inter, sans-serif" 
          font-size="9">Q3</text>
    <text x="100" y="75" text-anchor="middle" fill="#9ca3af" font-family="Inter, sans-serif" 
          font-size="9">Q4</text>
  </g>
  
  <!-- Elegant upward arrow -->
  <g transform="translate(250, 280)">
    <path d="M20 20 L10 30 L15 30 L15 45 L25 45 L25 30 L30 30 Z" fill="#2563eb" opacity="0.9"/>
    <circle cx="20" cy="15" r="3" fill="#2563eb"/>
  </g>
  
  <!-- Sleek gear icon -->
  <g transform="translate(300, 260)">
    <circle cx="15" cy="15" r="12" fill="#475569" opacity="0.1"/>
    <circle cx="15" cy="15" r="8" fill="#6b7280" opacity="0.8"/>
    <circle cx="15" cy="15" r="4" fill="#ffffff"/>
    <!-- Gear teeth -->
    <rect x="13" y="3" width="4" height="3" fill="#6b7280" opacity="0.8"/>
    <rect x="13" y="24" width="4" height="3" fill="#6b7280" opacity="0.8"/>
    <rect x="3" y="13" width="3" height="4" fill="#6b7280" opacity="0.8"/>
    <rect x="24" y="13" width="3" height="4" fill="#6b7280" opacity="0.8"/>
  </g>
  
  <!-- Flowing connection lines -->
  <path d="M240 180 Q260 180 280 190" stroke="#cbd5e1" stroke-width="2" 
        fill="none" stroke-linecap="round" opacity="0.6"/>
  <path d="M180 240 Q220 250 250 290" stroke="#cbd5e1" stroke-width="2" 
        fill="none" stroke-linecap="round" opacity="0.6"/>
  
  <!-- Subtle floating elements -->
  <circle cx="50" cy="300" r="2" fill="#dbeafe" opacity="0.7"/>
  <circle cx="350" cy="320" r="3" fill="#bfdbfe" opacity="0.6"/>
  <rect x="20" y="350" width="8" height="8" rx="2" fill="#e0e7ff" opacity="0.5"/>
  
  <!-- Professional title -->
  <text x="200" y="40" text-anchor="middle" fill="#1e293b" font-family="Inter, sans-serif" 
        font-size="18" font-weight="700">Practice Analytics</text>
  <text x="200" y="58" text-anchor="middle" fill="#64748b" font-family="Inter, sans-serif" 
        font-size="12" font-weight="400">Real-time insights for growth</text>
</svg>