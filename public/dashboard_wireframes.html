<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dental Practice Analytics Dashboard Wireframes</title>
    <style>
        body {
            font-family: Inter, -apple-system, sans-serif;
            background-color: #f8fafc;
            margin: 0;
            padding: 20px;
        }
        
        .wireframe-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .wireframe-header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .wireframe-section {
            margin: 20px 0;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .layout-grid {
            display: grid;
            gap: 16px;
            margin: 16px 0;
        }
        
        /* Main Dashboard Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: 64px 1fr;
            height: 100vh;
            max-height: 600px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .sidebar {
            grid-row: 1 / -1;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            padding: 16px;
        }
        
        .top-nav {
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            padding: 0 24px;
            justify-content: space-between;
        }
        
        .main-content {
            background: #f8fafc;
            padding: 24px;
            overflow-y: auto;
        }
        
        /* Component Boxes */
        .component-box {
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            background: rgba(248, 250, 252, 0.5);
            font-size: 0.875rem;
            color: #64748b;
        }
        
        .metric-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .chart-container {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
        }
        
        /* Grid Layouts for Different Sections */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 16px;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 16px;
            margin-top: 24px;
        }
        
        .production-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .data-table {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 24px;
        }
        
        /* Role-based Layout Indicators */
        .role-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 12px;
        }
        
        .office-manager { background: #dbeafe; color: #1e40af; }
        .dentist { background: #dcfce7; color: #166534; }
        .front-desk { background: #fef3c7; color: #92400e; }
        .admin { background: #e0e7ff; color: #3730a3; }
        
        .mobile-layout {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            max-width: 375px;
            margin: 0 auto;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
        }
        
        .mobile-nav {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            justify-content: space-around;
        }
        
        .annotation {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
            font-size: 0.875rem;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="wireframe-container">
        <div class="wireframe-header">
            <h1>Dental Practice Analytics Dashboard Wireframes</h1>
            <p>Based on design brief and production data structure</p>
        </div>
        
        <!-- Main Desktop Layout -->
        <div class="wireframe-section">
            <h2 class="section-title">1. Main Dashboard Layout (Desktop)</h2>
            <div class="annotation">
                <strong>Key Features:</strong> Role-based views, real-time production data, Google Sheets integration, responsive design
            </div>
            
            <div class="main-layout">
                <div class="sidebar">
                    <div class="component-box" style="margin-bottom: 16px;">
                        <strong>Clinic Logo</strong><br>
                        Practice Name
                    </div>
                    <div class="component-box" style="margin-bottom: 12px;">Dashboard</div>
                    <div class="component-box" style="margin-bottom: 12px;">Production</div>
                    <div class="component-box" style="margin-bottom: 12px;">Providers</div>
                    <div class="component-box" style="margin-bottom: 12px;">Reports</div>
                    <div class="component-box" style="margin-bottom: 12px;">Goals</div>
                    <div class="component-box" style="margin-bottom: 12px;">Settings</div>
                    <div style="margin-top: auto; padding-top: 20px;">
                        <div class="component-box">User Profile</div>
                    </div>
                </div>
                
                <div class="top-nav">
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div class="component-box" style="padding: 8px 16px;">📅 Date Picker</div>
                        <div class="component-box" style="padding: 8px 16px;">🔍 Search</div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div class="component-box" style="padding: 8px;">🔔</div>
                        <div class="component-box" style="padding: 8px;">⚙️</div>
                        <div class="component-box" style="padding: 8px;">👤</div>
                    </div>
                </div>
                
                <div class="main-content">
                    <div class="role-indicator office-manager">Office Manager View</div>
                    
                    <!-- Key Metrics Row -->
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div style="font-size: 2rem; font-weight: 700; color: #1e293b;">$12,450</div>
                            <div style="font-size: 0.75rem; color: #6b7280; text-transform: uppercase;">Today's Production</div>
                        </div>
                        <div class="metric-card">
                            <div style="font-size: 2rem; font-weight: 700; color: #059669;">98.2%</div>
                            <div style="font-size: 0.75rem; color: #6b7280; text-transform: uppercase;">Goal Achievement</div>
                        </div>
                        <div class="metric-card">
                            <div style="font-size: 2rem; font-weight: 700; color: #2563eb;">3</div>
                            <div style="font-size: 0.75rem; color: #6b7280; text-transform: uppercase;">Active Providers</div>
                        </div>
                        <div class="metric-card">
                            <div style="font-size: 2rem; font-weight: 700; color: #d97706;">+5.2%</div>
                            <div style="font-size: 0.75rem; color: #6b7280; text-transform: uppercase;">Variance vs Goal</div>
                        </div>
                    </div>
                    
                    <!-- Charts Section -->
                    <div class="charts-grid">
                        <div class="chart-container">
                            📊 Production Trend Chart<br>
                            <small>(Monthly production vs goals)</small>
                        </div>
                        <div class="chart-container">
                            🥧 Provider Breakdown<br>
                            <small>(Production by provider)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Provider-Specific Views -->
        <div class="wireframe-section">
            <h2 class="section-title">2. Production Data Views</h2>
            
            <!-- Hygienist Production View -->
            <div style="margin-bottom: 32px;">
                <div class="role-indicator dentist">Hygienist Production View</div>
                <div class="annotation">
                    <strong>Data Source:</strong> hygiene_production table - tracks hours_worked, estimated_production, verified_production, production_goal, variance_percentage, bonus_amount
                </div>
                
                <div class="production-grid">
                    <div class="chart-container">
                        📈 Daily Production vs Goal<br>
                        <small>estimated_production vs verified_production vs production_goal</small>
                    </div>
                    <div class="chart-container">
                        💰 Bonus Tracking<br>
                        <small>bonus_amount over time with variance_percentage</small>
                    </div>
                </div>
                
                <div class="data-table">
                    <div style="font-weight: 600; margin-bottom: 12px;">📋 Hygiene Production Table</div>
                    <div class="component-box">
                        Date | Hours | Est. Production | Verified Production | Goal | Variance % | Bonus
                    </div>
                </div>
            </div>
            
            <!-- Dentist Production View -->
            <div>
                <div class="role-indicator dentist">Dentist Production View</div>
                <div class="annotation">
                    <strong>Data Source:</strong> dentist_production table - tracks total_production, monthly_goal, production_per_hour, avg_daily_production
                </div>
                
                <div class="production-grid">
                    <div class="chart-container">
                        📈 Monthly Production vs Goal<br>
                        <small>total_production tracking against monthly_goal</small>
                    </div>
                    <div class="chart-container">
                        ⏱️ Efficiency Metrics<br>
                        <small>production_per_hour and avg_daily_production</small>
                    </div>
                </div>
                
                <div class="data-table">
                    <div style="font-weight: 600; margin-bottom: 12px;">📋 Dentist Production Table</div>
                    <div class="component-box">
                        Date | Total Production | Monthly Goal | Production/Hour | Daily Average
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Role-Based Dashboard Variations -->
        <div class="wireframe-section">
            <h2 class="section-title">3. Role-Based Dashboard Variations</h2>
            
            <div class="layout-grid">
                <!-- Office Manager View -->
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                    <div class="role-indicator office-manager">Office Manager Dashboard</div>
                    <div class="metrics-grid" style="grid-template-columns: repeat(3, 1fr);">
                        <div class="component-box">Total Practice Production</div>
                        <div class="component-box">Provider Performance</div>
                        <div class="component-box">Goal Achievement</div>
                        <div class="component-box">Hygienist Bonus Totals</div>
                        <div class="component-box">Production Variance</div>
                        <div class="component-box">Provider Count</div>
                    </div>
                </div>
                
                <!-- Dentist View -->
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                    <div class="role-indicator dentist">Dentist Dashboard</div>
                    <div class="metrics-grid" style="grid-template-columns: repeat(2, 1fr);">
                        <div class="component-box">My Production</div>
                        <div class="component-box">My Goals</div>
                        <div class="component-box">Location Comparison</div>
                        <div class="component-box">Efficiency Metrics</div>
                    </div>
                </div>
                
                <!-- Front Desk View -->
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                    <div class="role-indicator front-desk">Front Desk Dashboard</div>
                    <div class="metrics-grid" style="grid-template-columns: repeat(2, 1fr);">
                        <div class="component-box">Today's Production Summary</div>
                        <div class="component-box">Provider Status</div>
                        <div class="component-box">Goal Progress</div>
                        <div class="component-box">Daily Overview</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Key Components -->
        <div class="wireframe-section">
            <h2 class="section-title">4. Key Dashboard Components</h2>
            
            <div class="layout-grid">
                <!-- Data Visualization Components -->
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                    <h3 style="margin-top: 0;">📊 Chart Components</h3>
                    <div class="component-box" style="margin-bottom: 12px;">Line Chart: Production Trends Over Time</div>
                    <div class="component-box" style="margin-bottom: 12px;">Bar Chart: Provider Comparison</div>
                    <div class="component-box" style="margin-bottom: 12px;">Pie Chart: Production by Location</div>
                    <div class="component-box">Gauge: Goal Achievement %</div>
                </div>
                
                <!-- Data Tables -->
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                    <h3 style="margin-top: 0;">📋 Table Components</h3>
                    <div class="component-box" style="margin-bottom: 12px;">Sortable Production Data Table</div>
                    <div class="component-box" style="margin-bottom: 12px;">Provider Performance Table</div>
                    <div class="component-box" style="margin-bottom: 12px;">Goals vs Actual Table</div>
                    <div class="component-box">Exportable Reports Table</div>
                </div>
                
                <!-- Interactive Elements -->
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                    <h3 style="margin-top: 0;">🎛️ Interactive Elements</h3>
                    <div class="component-box" style="margin-bottom: 12px;">Date Range Picker</div>
                    <div class="component-box" style="margin-bottom: 12px;">Provider Filter Dropdown</div>
                    <div class="component-box" style="margin-bottom: 12px;">Location Toggle</div>
                    <div class="component-box">Real-time Refresh Button</div>
                </div>
            </div>
        </div>
        
        <!-- Mobile Responsive -->
        <div class="wireframe-section">
            <h2 class="section-title">5. Mobile Responsive Layout</h2>
            <div class="annotation">
                <strong>Responsive Strategy:</strong> Collapsed sidebar navigation, stacked metric cards, simplified charts with tap-to-expand
            </div>
            
            <div class="mobile-layout">
                <div style="background: #2563eb; color: white; padding: 12px; border-radius: 6px; text-align: center;">
                    <strong>Practice Dashboard</strong>
                </div>
                
                <div class="component-box">📊 Today's Summary Card</div>
                <div class="component-box">📈 Quick Production Chart</div>
                <div class="component-box">👥 Provider List</div>
                <div class="component-box">🎯 Goals Summary</div>
                
                <div class="mobile-nav">
                    <div class="component-box" style="padding: 8px;">🏠</div>
                    <div class="component-box" style="padding: 8px;">📊</div>
                    <div class="component-box" style="padding: 8px;">👥</div>
                    <div class="component-box" style="padding: 8px;">⚙️</div>
                </div>
            </div>
        </div>
        
        <!-- Technical Implementation Notes -->
        <div class="wireframe-section">
            <h2 class="section-title">6. Technical Implementation Notes</h2>
            
            <div class="annotation">
                <strong>Based on Current Data Structure:</strong>
                <ul style="margin: 8px 0; padding-left: 20px;">
                    <li><strong>hygiene_production:</strong> Daily tracking with hours, production estimates vs verified, goals, variance, and bonus calculations</li>
                    <li><strong>dentist_production:</strong> Daily production tracking with efficiency metrics (per hour, daily averages, monthly goals)</li>
                    <li><strong>providers:</strong> Link production data to specific dentists and hygienists</li>
                    <li><strong>clinics:</strong> Multi-tenant support for different practice locations</li>
                    <li><strong>Note:</strong> Office Manager specific metrics (like their bonuses) would need separate tables in future iterations</li>
                </ul>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                    <h3 style="margin-top: 0;">🎨 Design System Implementation</h3>
                    <ul style="font-size: 0.875rem; color: #475569;">
                        <li>Primary Blue (#2563eb) for navigation and CTAs</li>
                        <li>Success Green (#059669) for goal achievements</li>
                        <li>Warning Amber (#d97706) for alerts and targets</li>
                        <li>Professional white cards with subtle shadows</li>
                        <li>Inter font for optimal data readability</li>
                    </ul>
                </div>
                
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                    <h3 style="margin-top: 0;">⚡ Real-time Features</h3>
                    <ul style="font-size: 0.875rem; color: #475569;">
                        <li>Live production data sync from Google Sheets</li>
                        <li>Real-time goal progress indicators</li>
                        <li>Provider performance updates</li>
                        <li>Automatic bonus calculations</li>
                        <li>Multi-location data aggregation</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>