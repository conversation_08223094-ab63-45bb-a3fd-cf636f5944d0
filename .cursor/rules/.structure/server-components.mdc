---
description: Server Component Data Fetching with Supabase
globs: 
alwaysApply: false
---
Rule: Server Component Data Fetching with Supabase

Use the following pattern for server components that fetch data from Supabase:

1. Import the createClient from utils/supabase/server
2. Use the cookies() API from next/headers to get cookie store
3. Initialize Supabase client with the cookie store
4. Fetch data using the Supabase client
5. Place server component files appropriately in the file structure

Example:
```tsx
// src/app/your-route/page.tsx
import { createClient } from '@/utils/supabase/server'
import { cookies } from 'next/headers'

export default async function Page() {
  const cookieStore = cookies()
  const supabase = createClient(cookieStore)
  const { data: items } = await supabase.from('your_table').select()
  
  return (
    <div>
      {/* Render your data here */}
      <ul>
        {items?.map((item) => (
          <li key={item.id}>{item.name}</li>
        ))}
      </ul>
    </div>
  )
}
```

Placement Rules

Location: Place page.tsx files in directories that correspond to your intended routes:

/app/page.tsx - Home page
/app/dashboard/page.tsx - Dashboard page
/app/items/[id]/page.tsx - Dynamic item page


Utility Files: Keep Supabase utility files in a consistent location:

/utils/supabase/server.ts - For server components
/utils/supabase/client.ts - For client components
/utils/supabase/middleware.ts - For middleware


Authentication Pages: For login/auth pages, use:

/app/login/page.tsx
/app/register/page.tsx
/app/auth/callback/route.ts (for handling auth callbacks)


Protected Routes: For routes that require authentication, implement middleware checks:
typescript// src/middleware.ts
import { createClient } from '@/utils/supabase/middleware'

export const middleware = async (request) => {
  const { supabase, response } = createClient(request)
  // Check auth state and redirect if needed
}


These rules help maintain consistent code patterns across your project when using Supabase with Next.js server components.