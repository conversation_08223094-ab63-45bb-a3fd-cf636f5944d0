NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
# Site URL for authentication redirects
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Connect to Supabase via connection pooling
DATABASE_URL="postgresql://postgres.[YOUR_PROJECT_REF]:[YOUR_PASSWORD]@aws-0-us-east-2.pooler.supabase.com:6543/postgres?pgbouncer=true"

# Direct connection to the database. Used for migrations
DIRECT_URL="postgresql://postgres.[YOUR_PROJECT_REF]:[YOUR_PASSWORD]@aws-0-us-east-2.pooler.supabase.com:5432/postgres"

# Google API Credentials (for Google Sheets API)
# Create these at https://console.cloud.google.com/
# 1. Create a project
# 2. Enable Google Sheets API
# 3. Create OAuth 2.0 credentials
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REDIRECT_URI="http://localhost:3000/api/auth/callback/google"


