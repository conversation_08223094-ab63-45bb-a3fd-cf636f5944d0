name: Automatically create draft pull request

on:
  push:
    branches:
      # Only trigger on conventional branch naming patterns
      - "feature/**"
      - "feat/**"
      - "fix/**"
      - "hotfix/**"
      - "bugfix/**"
      - "chore/**"
      - "docs/**"
      - "refactor/**"
      - "perf/**"
      - "test/**"
      - "ci/**"
      - "build/**"

jobs:
  create_draft_pr:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history for better PR context

      - name: Check if PR already exists
        id: check_pr
        run: |
          # Check if a PR already exists for this branch
          PR_EXISTS=$(gh pr list --head "${{ github.ref_name }}" --json number --jq length)
          echo "pr_exists=$PR_EXISTS" >> $GITHUB_OUTPUT
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Get branch info for better PR title
        id: branch_info
        if: steps.check_pr.outputs.pr_exists == '0'
        run: |
          BRANCH_NAME="${{ github.ref_name }}"
          # Extract type and description from branch name
          if [[ $BRANCH_NAME =~ ^(feature|feat|fix|hotfix|bugfix|chore|docs|refactor|perf|test|ci|build)/(.+)$ ]]; then
            TYPE="${BASH_REMATCH[1]}"
            DESCRIPTION="${BASH_REMATCH[2]}"
            # Convert kebab-case to title case
            TITLE=$(echo "$DESCRIPTION" | sed 's/-/ /g' | sed 's/\b\w/\U&/g')
            echo "pr_title=${TYPE}: ${TITLE}" >> $GITHUB_OUTPUT
          else
            echo "pr_title=Draft: Changes from ${BRANCH_NAME}" >> $GITHUB_OUTPUT
          fi

      - name: Create draft pull request
        if: steps.check_pr.outputs.pr_exists == '0'
        uses: repo-sync/pull-request@v2
        with:
          source_branch: ${{ github.ref_name }}
          destination_branch: "main"
          pr_title: ${{ steps.branch_info.outputs.pr_title }}
          pr_body: |
            ## 🚧 Auto-generated Draft PR
            
            This draft pull request was automatically created for branch `${{ github.ref_name }}`.
            
            ### Branch Information
            - **Source Branch:** `${{ github.ref_name }}`
            - **Target Branch:** `main`
            - **Triggered by:** Push to branch
            
            ### Next Steps
            - [ ] Add a proper description of your changes
            - [ ] Update the PR title if needed
            - [ ] Add reviewers when ready
            - [ ] Mark as ready for review when complete
            
            ---
            *This PR was created automatically. Please update the description and title as needed.*
          pr_draft: true
          github_token: ${{ secrets.GITHUB_TOKEN }}

