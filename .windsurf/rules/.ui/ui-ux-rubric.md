---
trigger: model_decision
description: The rubric should be integrated as a final step in the component design process, where the LLM evaluates its own design recommendations against the established criteria.
globs: 
---
## UI/UX Design Rubric

After completing component recommendations, evaluate the design against our UI/UX rubric:

1. Reference the complete rubric at [ux-rubric.md](mdc:docs/ux-rubric.md) for evaluation criteria (located here: docs/ux-rubric.md)
2. Score each category from A (exemplary) to F (unsatisfactory)
3. Provide brief justification for each score
4. Identify improvement opportunities
5. Close with "Overall Grade" reflecting the weighted average

This self-evaluation ensures consistent quality across all UI components and identifies areas for improvement.

### Example Rubric Evaluation:

**Color Palette: B**  
Colors align with brand identity and maintain good contrast. Could be improved with more nuanced secondary palette.

**Layout & Grid: A**  
Expertly executed grid system with consistent spacing and perfect alignment throughout.

**Typography: B+**  
Strong typographic hierarchy with appropriate font selections. Minor kerning adjustments recommended.

**Hierarchy & Navigation: B**  
Clear content levels and accessible navigation. Consider enhancing prominence of primary actions.

**Accessibility: A-**  
Exceeds WCAG AA standards with thoughtful keyboard navigation and ARIA implementation.

**Spacing & Alignment: B**  
Thoughtful use of white space creates clean layout. Footer alignment needs minor adjustment.

**Overall Grade: B+**  
Strong design with excellent grid implementation and accessibility. Minor refinements to color palette and navigation prominence would elevate to A-level.