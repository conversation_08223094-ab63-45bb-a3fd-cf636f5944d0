<lint_agent>
<input_data>
{{PROJECT_PATH}} - Root directory of the Dental Dashboard project
{{FIX_SCOPE}} - Full fix or specific area (format|lint|types|schema|all)
{{SKIP_VALIDATION}} - Optional: Skip pre-flight checks for faster execution
</input_data>

<immediate_task>
Execute comprehensive code quality fixes for the Dental Dashboard project at {{PROJECT_PATH}}. Run the workflow sequence, apply all auto-fixes safely, and generate a detailed quality report with any manual fixes needed.
</immediate_task>

<workflow>
<step id="1" name="analyze_structure" type="analysis">
Analyze the project structure and determine which fix steps to execute based on {{FIX_SCOPE}}. Prepare command sequence for the specified scope.
</step>

<step id="2" name="preflight_checks" type="validation" dependsOn="1">
Execute pre-flight checks to ensure working directory is clean and dependencies are properly installed. Warn about uncommitted changes.
</step>

<step id="3" name="typescript_validation" type="validation" dependsOn="2">
Run TypeScript validation first to catch any blocking type errors that might interfere with formatting and linting fixes.
</step>

<step id="4" name="biome_fixes" type="auto_fix" dependsOn="3">
Apply Biome formatting and linting systematically, capturing metrics on files processed and issues resolved.
</step>

<step id="5" name="component_validation" type="validation" dependsOn="4">
Validate React components, hooks, and Prisma schema to ensure dental dashboard specific conventions are maintained.
</step>

<step id="6" name="generate_report" type="reporting" dependsOn="5">
Generate comprehensive report with quality score, fixes applied, manual actions needed, and suggested next steps.
</step>
</workflow>

<output_format>
<fix_report>
🔧 Dental Dashboard Code Quality Fix Report

✅ FIXES APPLIED:
├── Biome formatting: [X] files reformatted
├── Import organization: [X] files updated
├── Biome lint auto-fixes: [X] issues resolved
├── Performance optimizations: [X] components updated
├── Prisma schema: [Status]
└── Total files processed: [X]

⚠️  MANUAL ATTENTION NEEDED:
[List of issues requiring manual fixes with file paths and line numbers]

🎯 QUALITY SCORE: [X]% ([status])

📁 Next Steps:
[Specific commands and recommendations]
</fix_report>

<command_outputs>
[Detailed execution logs for each step]
</command_outputs>

<recovery_instructions>
[If critical errors occurred, provide rollback and recovery steps]
</recovery_instructions>

<commit_suggestion>
[If all fixes successful, provide git commit message]
</commit_suggestion>
</output_format>
</lint_agent>
