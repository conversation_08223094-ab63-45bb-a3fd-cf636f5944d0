<emergency_brake>
If at any point the AI suggests changes that exceed constraints, use this prompt:

<stop_command>
STOP. You've exceeded the constraint limits.
</stop_command>

<required_response>
<constraint_violated>
Identify which constraint was violated
</constraint_violated>

<corrective_action>
Break this into smaller, compliant steps
</corrective_action>

Recommend only the first small step
</safe_first_step>

<safety_explanation>
Explain why this approach is safer
</safety_explanation>
</required_response>
</emergency_brake>
