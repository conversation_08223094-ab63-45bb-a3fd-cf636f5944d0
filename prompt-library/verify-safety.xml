<task>
Review the changes made to `[FILENAME]` and verify safety.
</task>

<validation_checklist>
<compatibility>
Do these changes affect any other files?
</compatibility>

<breaking_changes>
Are there potential breaking changes?
</breaking_changes>

<test_requirements>
What tests should be run to verify this works?
</test_requirements>

<next_file>
What's the next safest file to process?
</next_file>
</validation_checklist>

<response_format>
<compatibility_check>
PASS/FAIL with explanation
</compatibility_check>

<test_requirements>
Specific tests to run
</test_requirements>

<impact_verification>
Steps to check for side effects
</impact_verification>

<next_recommendation>
Safest next file and reason
</next_recommendation>
</response_format>
