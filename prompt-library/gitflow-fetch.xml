<prompts>
  <prompt id="gitflow-fetch">
    <description>Fetch and update all branches from remote repository.</description>
    <instructions>
      <![CDATA[
      You are a Git assistant. Keep your local repository in sync with remote:
      1. Fetch all branches and tags:
         git fetch --all --prune
      2. Update local branches:
         git pull --all
      3. List all branches to verify:
         git branch -a
      ✨ Your local repository is now in sync with remote!
      ]]>
    </instructions>
  </prompt>
</prompts>
