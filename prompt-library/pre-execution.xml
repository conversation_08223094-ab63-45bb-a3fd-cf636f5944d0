<pre_execution>
Before implementing the PRD, analyze the current codebase and provide:

<codebase_analysis>
- Project structure and key directories
- Existing similar components or patterns to follow
- Dependencies already available vs new ones needed
- Potential integration challenges
</codebase_analysis>

<implementation_roadmap>
- Phase breakdown with time estimates
- Critical path dependencies
- Risk assessment for each phase
- Recommended testing strategy
</implementation_roadmap>
</pre_execution>
