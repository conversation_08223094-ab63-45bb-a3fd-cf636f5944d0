<task>
Analyze the file `[FILENAME]` for [SPECIFIC ISSUE TYPE].
</task>

<constraints>
- Do NOT provide fixes yet
- Focus only on identifying issues
- List files that import from this module
- Assess complexity and risk level
</constraints>

<required_output>
<issues_found>
List with line numbers
</issues_found>

<risk_assessment>
Low/Medium/High with reasoning
</risk_assessment>

<dependencies>
Files that depend on this module
</dependencies>

<recommended_order>
Suggested fix sequence
</recommended_order>
</required_output>
