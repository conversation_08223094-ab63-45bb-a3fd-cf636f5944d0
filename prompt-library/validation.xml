<post_file_validation>
After each file is processed, use this validation prompt:

<task>
Before proceeding to the next file, verify the current changes.
</task>

<validation_requirements>
<compilation_check>
Confirm these changes compile successfully
</compilation_check>

<error_assessment>
List any new errors introduced
</error_assessment>

<issue_resolution>
Verify the specific issue was resolved
</issue_resolution>

<next_file_planning>
Recommend the next safest file to process
</next_file_planning>

<progress_estimation>
Estimate remaining work (number of files/sessions needed)
</progress_estimation>
</validation_requirements>
</post_file_validation>
