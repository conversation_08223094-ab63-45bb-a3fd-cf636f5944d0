<task>
Before making any changes to `[FILENAME]`, work through this analysis systematically.
</task>

<thinking>
<step1>
Current State Analysis:
- Analyze current code structure
- Identify the specific issues
- Map out dependencies
</step1>

<step2>
Dependency Impact Assessment:
- List importing files
- Identify external API usage
- Assess breaking change potential
</step2>

<step3>
Minimal Fix Strategy:
- Identify smallest possible change
- Verify it doesn't break contracts
- Ensure it's within 15-line limit
</step3>

<step4>
Validation Planning:
- Specific test requirements
- Compilation checks needed
- Runtime verification steps
</step4>
</thinking>

<implementation>
Now provide the implementation following the constraints above.
</implementation>
