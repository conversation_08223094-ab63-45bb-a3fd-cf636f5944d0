/**
 * @fileoverview Supabase type definitions for the dental dashboard application.
 *
 * This file contains TypeScript type definitions specific to Supabase integration,
 * including authentication types, database schema types, and utility types for
 * working with Supabase features in a type-safe manner.
 *
 * These types complement the auto-generated Prisma types and provide
 * additional type safety when working with Supabase-specific features.
 */

/**
 * Placeholder for Supabase type definitions.
 * Extend this file as needed when implementing Supabase-specific features.
 */
