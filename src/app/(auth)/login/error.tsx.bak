/**
 * Login Error Component
 *
 * This client-side component is automatically rendered by Next.js when an error
 * occurs during the rendering of the login page. It provides a user-friendly
 * error message and a way to recover from the error by attempting to reload
 * the page.
 *
 * This component is part of Next.js error handling system and is triggered when:
 * - There's a runtime error in the login page component
 * - An uncaught exception occurs during rendering
 * - A server component fails to load data
 */

'use client';

import { Button } from '@/components/ui/button';
import { useEffect } from 'react';

/**
 * Login Error Component
 *
 * Displays a user-friendly error message when the login page fails to load
 * and provides a button to attempt recovery by resetting the error boundary.
 *
 * @param {Object} props - Component props
 * @param {Error & { digest?: string }} props.error - The error that occurred
 *   The digest is a unique identifier for the error generated by Next.js
 * @param {() => void} props.reset - Function to reset the error boundary and retry rendering
 * @returns {JSX.Element} The rendered error component
 */
export default function LoginError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  // Log the error to the console for debugging purposes
  useEffect(() => {}, [error]);

  return (
    <div class="flex h-screen flex-col items-center justify-center">
      <div class="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
        <h2 class="mb-2 text-2xl font-semibold">Something went wrong!</h2>
        <p class="mb-4 text-muted-foreground">
          An error occurred while trying to load the login page.
        </p>
        <Button onClick={() => reset()} class="mt-4">
          Try again
        </Button>
      </div>
    </div>
  );
}
