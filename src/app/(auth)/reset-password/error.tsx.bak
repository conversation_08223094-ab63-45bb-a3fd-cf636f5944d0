/**
 * Reset Password Error Component
 *
 * This client-side component is automatically rendered by Next.js when an error
 * occurs during the rendering of the reset password page. It provides a user-friendly
 * error message and options to recover from the error by either retrying or
 * returning to the login page.
 *
 * This component is part of Next.js error handling system and is triggered when:
 * - There's a runtime error in the reset password page component
 * - An uncaught exception occurs during rendering
 * - The password reset API call fails
 * - Token validation fails
 */

'use client';

import { Button } from '@/components/ui/button';
import { useEffect } from 'react';

/**
 * Reset Password Error Component
 *
 * Displays a user-friendly error message when the password reset page fails to load
 * or process a request, and provides options to retry or navigate back to login.
 *
 * @param {Object} props - Component props
 * @param {Error & { digest?: string }} props.error - The error that occurred
 *   The digest is a unique identifier for the error generated by Next.js
 * @param {() => void} props.reset - Function to reset the error boundary and retry rendering
 * @returns {JSX.Element} The rendered error component with retry and navigation options
 */
export default function ResetPasswordError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {}, [error]);

  return (
    <div class="min-h-screen flex flex-col items-center justify-center bg-[#121212] relative overflow-hidden w-full rounded-xl">
      <div class="relative z-10 w-full max-w-sm rounded-3xl bg-gradient-to-r from-[#ffffff10] to-[#121212] backdrop-blur-sm shadow-2xl p-8 flex flex-col items-center">
        <div class="flex items-center justify-center w-24 h-24 rounded-full bg-red-500/20 mb-6 shadow-lg">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-12 w-12 text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>

        <h2 class="text-2xl font-semibold text-white mb-4 text-center">Something went wrong</h2>

        <p class="text-gray-300 mb-6 text-center">
          We encountered an error while trying to process your password reset request.
        </p>

        <div class="flex flex-col w-full gap-4">
          <Button
            onClick={reset}
            class="w-full bg-white/10 text-white font-medium px-5 py-3 rounded-full shadow hover:bg-white/20 transition mb-3 text-sm"
          >
            Try again
          </Button>

          <a href="/login" class="w-full text-center text-sm text-gray-400 hover:text-white">
            Return to login
          </a>
        </div>
      </div>
    </div>
  );
}
