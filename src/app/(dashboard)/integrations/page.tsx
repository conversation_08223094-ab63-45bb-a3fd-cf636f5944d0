/**
 * Integrations Page
 * Main page for managing data integrations and connections
 */
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FileSpreadsheet } from 'lucide-react';

export default function IntegrationsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Integrations</h1>
        <p className="text-muted-foreground">
          Connect your practice management systems and data sources
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="opacity-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileSpreadsheet className="h-5 w-5" />
              Google Sheets
            </CardTitle>
            <CardDescription>Data synchronization handled via automated scripts</CardDescription>
          </CardHeader>
          <CardContent>
            <Button disabled={true} className="w-full">
              Managed by Admin
            </Button>
          </CardContent>
        </Card>

        <Card className="opacity-50">
          <CardHeader>
            <CardTitle>Practice Management</CardTitle>
            <CardDescription>
              Connect your practice management software (Coming Soon)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button disabled={true} className="w-full">
              Coming Soon
            </Button>
          </CardContent>
        </Card>

        <Card className="opacity-50">
          <CardHeader>
            <CardTitle>Imaging Systems</CardTitle>
            <CardDescription>Connect your digital imaging systems (Coming Soon)</CardDescription>
          </CardHeader>
          <CardContent>
            <Button disabled={true} className="w-full">
              Coming Soon
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
